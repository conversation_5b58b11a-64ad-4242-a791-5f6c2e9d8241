'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { UserRole } from '@prisma/client'
import { toast, Toaster } from 'sonner'
import {
  useWorkingHours,
  useTimeBlocks,
  useTimeOff,
  useScheduleTemplates
} from '@/hooks/useScheduleAPI'
import type {
  WorkingHoursFormData,
  TimeBlockFormData,
  TimeOffFormData
} from '@/lib/validations/schedule'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import {
  Clock,
  Calendar,
  Settings,
  Plus,
  Edit,
  Loader2,
  CalendarDays,
  Timer,
  Repeat,
  Plane
} from 'lucide-react'

// Import our new schedule components
import {
  WeeklyScheduleGrid,
  TimeSlotPicker,
  RecurringPatternSelector,
  HolidayCalendar,
  TimeBlockingInterface
} from '@/components/schedule'

// Mock data - in real implementation, this would come from API calls
const mockScheduleData = [
  {
    date: new Date(),
    dayOfWeek: new Date().getDay(),
    isWorkingDay: true,
    timeSlots: [
      {
        id: '1',
        startTime: '09:00',
        endTime: '12:00',
        title: 'Morning Clinic',
        type: 'working' as const,
        description: 'Regular patient appointments'
      },
      {
        id: '2',
        startTime: '12:00',
        endTime: '13:00',
        title: 'Lunch Break',
        type: 'break' as const
      },
      {
        id: '3',
        startTime: '13:00',
        endTime: '17:00',
        title: 'Afternoon Clinic',
        type: 'working' as const,
        description: 'Regular patient appointments'
      }
    ]
  }
]

const mockHolidays = [
  {
    id: '1',
    name: 'New Year\'s Day',
    date: new Date('2024-01-01'),
    type: 'national' as const,
    description: 'National Holiday'
  },
  {
    id: '2',
    name: 'Clinic Maintenance',
    date: new Date('2024-02-15'),
    type: 'clinic' as const,
    description: 'Equipment maintenance day'
  }
]

const mockTimeBlocks = [
  {
    id: '1',
    title: 'Root Canal Procedure',
    startTime: '14:00',
    endTime: '16:00',
    date: new Date(),
    type: 'procedure' as const,
    description: 'Complex root canal treatment - extra time needed'
  }
]

export default function DentistSchedule() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [selectedDate] = useState(new Date())
  const [activeTab, setActiveTab] = useState('overview')

  // Confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean
    title: string
    description: string
    onConfirm: () => Promise<void>
    loading: boolean
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: async () => {},
    loading: false
  })

  // API hooks for schedule management
  const workingHours = useWorkingHours()
  const timeBlocks = useTimeBlocks()
  const timeOff = useTimeOff()
  const scheduleTemplates = useScheduleTemplates()

  // Destructure fetch functions for stable references
  const { fetchWorkingHours } = workingHours
  const { fetchTimeBlocks } = timeBlocks
  const { fetchTimeOff } = timeOff
  const { fetchTemplates } = scheduleTemplates

  // Load initial data
  useEffect(() => {
    if (isAuthenticated && user?.role === UserRole.DENTIST) {
      // Add error handling to prevent infinite loops
      const loadData = async () => {
        try {
          await Promise.all([
            fetchWorkingHours(),
            fetchTimeBlocks(),
            fetchTimeOff(),
            fetchTemplates()
          ])
        } catch (error) {
          console.error('Failed to load schedule data:', error)
          // Don't retry automatically to prevent infinite loops
        }
      }
      loadData()
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      // Redirect non-dentist users
      router.push('/dashboard')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isAuthenticated,
    user?.role,
    router
    // Remove the fetch functions from dependencies to prevent infinite loops
  ])

  // API handler functions
  const handleAddTimeSlot = async (date: Date, time: string) => {
    try {
      const timeSlotData: WorkingHoursFormData = {
        dayOfWeek: date.getDay(),
        startTime: time,
        endTime: time, // This would be set by the user in a form
        isActive: true
      }
      await workingHours.createWorkingHour(timeSlotData)
    } catch (error) {
      console.error('Failed to add time slot:', error)
    }
  }

  const handleAddTimeBlock = async (block: any) => {
    try {
      const timeBlockData: TimeBlockFormData = {
        title: block.title,
        startTime: block.startTime,
        endTime: block.endTime,
        date: block.date,
        type: block.type,
        description: block.description,
        isRecurring: block.isRecurring || false
      }
      await timeBlocks.createTimeBlock(timeBlockData)
    } catch (error) {
      console.error('Failed to add time block:', error)
    }
  }

  const handleAddHoliday = async (holiday: any) => {
    try {
      const holidayData: TimeOffFormData = {
        startDate: holiday.date,
        endDate: holiday.date,
        type: 'personal',
        title: holiday.name,
        description: holiday.description,
        isAllDay: true
      }
      await timeOff.createTimeOff(holidayData)
    } catch (error) {
      console.error('Failed to add holiday:', error)
    }
  }

  // State to store the current pattern for saving later
  const [currentPattern, setCurrentPattern] = useState<any>(null)

  const handlePatternChange = (pattern: any) => {
    // Only store the pattern, don't show success message
    setCurrentPattern(pattern)
    console.log('Pattern changed:', pattern)
  }

  const handleEditTimeSlot = async (slot: any) => {
    try {
      // This would open an edit dialog or form
      toast.info('Edit functionality coming soon')
      console.log('Edit slot:', slot)
    } catch (error) {
      console.error('Failed to edit time slot:', error)
    }
  }

  const handleDeleteTimeSlot = async (slot: any) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Time Slot',
      description: `Are you sure you want to delete the time slot "${slot.title || 'Untitled'}"? This action cannot be undone.`,
      loading: false,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }))
        try {
          // API call would go here
          toast.success('Time slot deleted successfully')
          console.log('Delete slot:', slot)
        } catch (error) {
          toast.error('Failed to delete time slot')
          console.error('Failed to delete time slot:', error)
        } finally {
          setConfirmDialog(prev => ({ ...prev, loading: false }))
        }
      }
    })
  }

  const handleEditTimeBlock = async (block: any) => {
    try {
      toast.info('Edit time block functionality coming soon')
      console.log('Edit time block:', block)
    } catch (error) {
      console.error('Failed to edit time block:', error)
    }
  }

  const handleDeleteTimeBlock = async (blockId: string) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Time Block',
      description: 'Are you sure you want to delete this time block? This action cannot be undone.',
      loading: false,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }))
        try {
          await timeBlocks.deleteTimeBlock(blockId)
          toast.success('Time block deleted successfully')
        } catch (error) {
          toast.error('Failed to delete time block')
          console.error('Failed to delete time block:', error)
        } finally {
          setConfirmDialog(prev => ({ ...prev, loading: false }))
        }
      }
    })
  }

  const handleEditHoliday = async (holiday: any) => {
    try {
      toast.info('Edit holiday functionality coming soon')
      console.log('Edit holiday:', holiday)
    } catch (error) {
      console.error('Failed to edit holiday:', error)
    }
  }

  const handleDeleteHoliday = async (holidayId: string) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Holiday',
      description: 'Are you sure you want to delete this holiday? This action cannot be undone.',
      loading: false,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }))
        try {
          await timeOff.deleteTimeOff(holidayId)
          toast.success('Holiday deleted successfully')
        } catch (error) {
          toast.error('Failed to delete holiday')
          console.error('Failed to delete holiday:', error)
        } finally {
          setConfirmDialog(prev => ({ ...prev, loading: false }))
        }
      }
    })
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/schedule')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  // Show error state if there are authentication/profile issues
  if (workingHours.error?.includes('Dentist profile not found') ||
      timeBlocks.error?.includes('Dentist profile not found') ||
      timeOff.error?.includes('Dentist profile not found') ||
      scheduleTemplates.error?.includes('Dentist profile not found')) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-red-600">Profile Setup Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Your dentist profile is not set up properly. Please contact your administrator to complete your profile setup.
            </p>
            <Button onClick={() => router.push('/dentist/dashboard')} className="w-full">
              Return to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Schedule Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your availability, working hours, and time blocks
            </p>
          </div>
          <div className="flex space-x-3">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Quick Setup
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Quick Schedule Setup</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <TimeSlotPicker
                    title="Set Working Hours"
                    description="Configure your daily working hours"
                    allowCustomTitle={false}
                  />
                </div>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Block Time
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Block Time Slot</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <TimeSlotPicker
                    title="Block Time"
                    description="Block time for procedures or personal use"
                    allowCustomTitle={true}
                  />
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Current Schedule Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card className="border-blue-200 bg-blue-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-900">Today&apos;s Hours</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">9:00 AM - 5:00 PM</div>
            <p className="text-xs text-blue-700">8 hours scheduled</p>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-900">Available Slots</CardTitle>
            <Calendar className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">4</div>
            <p className="text-xs text-green-700">Open appointments today</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-purple-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-900">Time Blocks</CardTitle>
            <Timer className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">2</div>
            <p className="text-xs text-purple-700">Blocked slots today</p>
          </CardContent>
        </Card>

        <Card className="border-orange-200 bg-orange-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-900">Weekly Hours</CardTitle>
            <CalendarDays className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">40</div>
            <p className="text-xs text-orange-700">Hours this week</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Schedule Management Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-gray-100">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Weekly View</span>
          </TabsTrigger>
          <TabsTrigger value="timeblocks" className="flex items-center space-x-2">
            <Timer className="h-4 w-4" />
            <span>Time Blocks</span>
          </TabsTrigger>
          <TabsTrigger value="recurring" className="flex items-center space-x-2">
            <Repeat className="h-4 w-4" />
            <span>Patterns</span>
          </TabsTrigger>
          <TabsTrigger value="holidays" className="flex items-center space-x-2">
            <Plane className="h-4 w-4" />
            <span>Holidays</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {workingHours.loading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading schedule...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <WeeklyScheduleGrid
                weekStart={new Date()}
                schedule={mockScheduleData}
                onAddTimeSlot={handleAddTimeSlot}
                onEditTimeSlot={handleEditTimeSlot}
                onDeleteTimeSlot={handleDeleteTimeSlot}
                className="min-h-[600px]"
              />

              {/* Save/Cancel Actions */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-end space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Reset/refresh schedule
                        workingHours.fetchWorkingHours()
                        toast.info('Schedule refreshed')
                      }}
                    >
                      Refresh
                    </Button>
                    <Button
                      onClick={() => {
                        toast.success('Schedule saved successfully')
                      }}
                      disabled={workingHours.loading}
                    >
                      {workingHours.loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      Save Changes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="timeblocks" className="space-y-6">
          {timeBlocks.loading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading time blocks...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <TimeBlockingInterface
                timeBlocks={mockTimeBlocks}
                selectedDate={selectedDate}
                onAddTimeBlock={handleAddTimeBlock}
                onEditTimeBlock={handleEditTimeBlock}
                onDeleteTimeBlock={handleDeleteTimeBlock}
              />

              {/* Save/Cancel Actions */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-end space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Reset/refresh time blocks
                        timeBlocks.fetchTimeBlocks()
                        toast.info('Time blocks refreshed')
                      }}
                    >
                      Refresh
                    </Button>
                    <Button
                      onClick={() => {
                        toast.success('Time blocks saved successfully')
                      }}
                      disabled={timeBlocks.loading}
                    >
                      {timeBlocks.loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      Save Changes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="recurring" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recurring Schedule Patterns</CardTitle>
              <p className="text-sm text-gray-600">
                Set up recurring patterns for your regular working hours
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <RecurringPatternSelector
                onChange={handlePatternChange}
                className="max-w-2xl"
              />

              {/* Save/Cancel Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    // Reset form logic would go here
                    toast.info('Pattern changes cancelled')
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={async () => {
                    try {
                      if (!currentPattern) {
                        toast.error('Please configure a pattern first')
                        return
                      }

                      if (!user) {
                        toast.error('User not authenticated')
                        return
                      }

                      // Get dentist profile
                      const dentistResponse = await fetch('/api/dentist/profile')
                      if (!dentistResponse.ok) {
                        throw new Error('Failed to get dentist profile')
                      }
                      const dentistData = await dentistResponse.json()
                      const dentistId = dentistData.data?.id

                      if (!dentistId) {
                        throw new Error('Dentist profile not found')
                      }

                      // Convert pattern to working hours format
                      const workingHours = currentPattern.daysOfWeek?.map((dayOfWeek: number) => ({
                        dayOfWeek,
                        startTime: currentPattern.startTime || '09:00',
                        endTime: currentPattern.endTime || '17:00',
                        isActive: true
                      })) || []

                      // Create template with proper structure
                      const templateData = {
                        name: `${currentPattern.type.charAt(0).toUpperCase() + currentPattern.type.slice(1)} Pattern`,
                        description: `Auto-generated from ${currentPattern.type} recurring pattern`,
                        workingHours,
                        timeBlocks: [],
                        isDefault: false
                      }

                      await scheduleTemplates.createTemplate(templateData)
                      toast.success('Pattern saved successfully')
                    } catch (error) {
                      console.error('Failed to save pattern:', error)
                      toast.error('Failed to save pattern')
                    }
                  }}
                  disabled={scheduleTemplates.loading || !currentPattern}
                >
                  {scheduleTemplates.loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Save Pattern
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="holidays" className="space-y-6">
          {timeOff.loading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading holidays...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <HolidayCalendar
                holidays={mockHolidays}
                onAddHoliday={handleAddHoliday}
                onEditHoliday={handleEditHoliday}
                onDeleteHoliday={handleDeleteHoliday}
              />

              {/* Save/Cancel Actions */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-end space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Reset/refresh holidays
                        timeOff.fetchTimeOff()
                        toast.info('Holidays refreshed')
                      }}
                    >
                      Refresh
                    </Button>
                    <Button
                      onClick={() => {
                        toast.success('Holiday settings saved successfully')
                      }}
                      disabled={timeOff.loading}
                    >
                      {timeOff.loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      Save Changes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {scheduleTemplates.loading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading settings...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Default Working Hours</CardTitle>
                  <p className="text-sm text-gray-600">
                    Set your standard working hours for new schedule templates
                  </p>
                </CardHeader>
                <CardContent>
                  <TimeSlotPicker
                    title="Standard Hours"
                    description="Your typical working day"
                    allowCustomTitle={false}
                    mode="form"
                    showActions={true}
                    loading={workingHours.loading}
                    onSave={async (data) => {
                      await workingHours.createWorkingHour({
                        dayOfWeek: new Date().getDay(),
                        startTime: data.startTime,
                        endTime: data.endTime,
                        isActive: true
                      })
                    }}
                  />
                </CardContent>
              </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Schedule Preferences</CardTitle>
                <p className="text-sm text-gray-600">
                  Configure your schedule management preferences
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Button className="w-full" variant="outline">
                    <Settings className="h-4 w-4 mr-2" />
                    Appointment Buffer Time
                  </Button>
                  <Button className="w-full" variant="outline">
                    <Clock className="h-4 w-4 mr-2" />
                    Break Duration Settings
                  </Button>
                  <Button className="w-full" variant="outline">
                    <Calendar className="h-4 w-4 mr-2" />
                    Booking Window Limits
                  </Button>
                  <Button className="w-full" variant="outline">
                    <Edit className="h-4 w-4 mr-2" />
                    Emergency Slot Configuration
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        richColors
        closeButton
        duration={4000}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={confirmDialog.onConfirm}
        loading={confirmDialog.loading}
        variant="destructive"
        confirmText="Delete"
        cancelText="Cancel"
      />
    </div>
  )
}
