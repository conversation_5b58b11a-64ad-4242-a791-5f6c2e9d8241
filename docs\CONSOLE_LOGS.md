page-310a7180819d4833.js:1 
            
            
           POST http://localhost:3000/api/dentist/schedule/working-hours 400 (Bad Request)
(anonymous) @ page-310a7180819d4833.js:1
eo @ page-310a7180819d4833.js:1
onClick @ page-310a7180819d4833.js:1
iX @ 4bd1b696-f495732fc8ec87ec.js:1
(anonymous) @ 4bd1b696-f495732fc8ec87ec.js:1
nS @ 4bd1b696-f495732fc8ec87ec.js:1
i2 @ 4bd1b696-f495732fc8ec87ec.js:1
s7 @ 4bd1b696-f495732fc8ec87ec.js:1
s5 @ 4bd1b696-f495732fc8ec87ec.js:1
1684-9bd53adb6b17a5e0.js:1 Failed to add time slot: Error: Invalid input data
    at Object.createWorkingHour (page-310a7180819d4833.js:1:51059)
    at async eo (page-310a7180819d4833.js:1:59397)
overrideMethod @ hook.js:608
window.console.error @ 1684-9bd53adb6b17a5e0.js:1
eo @ page-310a7180819d4833.js:1
await in eo
onClick @ page-310a7180819d4833.js:1
iX @ 4bd1b696-f495732fc8ec87ec.js:1
(anonymous) @ 4bd1b696-f495732fc8ec87ec.js:1
nS @ 4bd1b696-f495732fc8ec87ec.js:1
i2 @ 4bd1b696-f495732fc8ec87ec.js:1
s7 @ 4bd1b696-f495732fc8ec87ec.js:1
s5 @ 4bd1b696-f495732fc8ec87ec.js:1
