import { PrismaClient, UserRole, UserStatus, AppointmentStatus, AppointmentType, PaymentStatus, PaymentMethod, TreatmentStatus, ToothCondition, NotificationChannel, NotificationType, TimeBlockType, TimeOffType, TimeOffStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

// Helper function to generate random dates
function getRandomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// Helper function to generate random time slots
function generateTimeSlots(startHour: number, endHour: number, intervalMinutes: number = 30): string[] {
  const slots: string[] = []
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += intervalMinutes) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      slots.push(timeString)
    }
  }
  return slots
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...')

  // Clear existing data in proper order to avoid foreign key constraints
  console.log('🧹 Clearing existing data...')
  await prisma.auditLog.deleteMany()
  await prisma.notification.deleteMany()
  await prisma.prescription.deleteMany()
  await prisma.treatmentFile.deleteMany()
  await prisma.treatment.deleteMany()
  await prisma.payment.deleteMany()
  await prisma.invoiceItem.deleteMany()
  await prisma.invoice.deleteMany()
  await prisma.appointment.deleteMany()
  await prisma.toothRecord.deleteMany()
  await prisma.dentalChart.deleteMany()
  await prisma.patientFile.deleteMany()
  await prisma.stockMovement.deleteMany()
  await prisma.inventoryUsage.deleteMany()
  await prisma.inventoryItem.deleteMany()
  await prisma.branchService.deleteMany()
  await prisma.branchDentist.deleteMany()
  await prisma.branchStaff.deleteMany()
  await prisma.service.deleteMany()
  // Clear schedule management data
  await prisma.dentistScheduleTemplate.deleteMany()
  await prisma.dentistTimeOff.deleteMany()
  await prisma.dentistTimeBlock.deleteMany()
  await prisma.dentistWorkingHours.deleteMany()
  await prisma.dentistSchedule.deleteMany()
  await prisma.patientProfile.deleteMany()
  await prisma.dentistProfile.deleteMany()
  await prisma.staffProfile.deleteMany()
  await prisma.branch.deleteMany()
  await prisma.account.deleteMany()
  await prisma.session.deleteMany()
  await prisma.user.deleteMany()

  // Hash password for test users
  const hashedPassword = await bcrypt.hash('password123', 12)

  console.log('👤 Creating user accounts...')

  // Create Admin User
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Dr. Sarah Johnson',
      phone: '+**********',
      password: hashedPassword,
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      emailVerified: new Date(),
      image: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400&h=400&fit=crop&crop=face',
    },
  })

  // Create Google OAuth account for admin (for testing OAuth integration)
  await prisma.account.create({
    data: {
      userId: admin.id,
      type: 'oauth',
      provider: 'google',
      providerAccountId: 'google_admin_123',
      access_token: 'mock_access_token_admin',
      token_type: 'Bearer',
      scope: 'openid email profile',
    },
  })

  // Create Staff Users
  const staff1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Maria Rodriguez',
      phone: '+**********',
      password: hashedPassword,
      role: UserRole.STAFF,
      status: UserStatus.ACTIVE,
      emailVerified: new Date(),
      image: 'https://images.unsplash.com/photo-*************-d0c2b8e3b5e3?w=400&h=400&fit=crop&crop=face',
      staffProfile: {
        create: {
          position: 'Dental Assistant',
          department: 'Clinical',
          hireDate: new Date('2023-01-15'),
          salary: 45000,
          permissions: ['manage_appointments', 'manage_patients', 'manage_inventory'],
        },
      },
    },
    include: {
      staffProfile: true,
    },
  })

  const staff2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Jennifer Lee',
      phone: '+**********',
      password: hashedPassword,
      role: UserRole.STAFF,
      status: UserStatus.ACTIVE,
      emailVerified: new Date(),
      image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=400&h=400&fit=crop&crop=face',
      staffProfile: {
        create: {
          position: 'Receptionist',
          department: 'Front Office',
          hireDate: new Date('2023-03-01'),
          salary: 38000,
          permissions: ['manage_appointments', 'manage_patients'],
        },
      },
    },
    include: {
      staffProfile: true,
    },
  })

  // Create Dentist Users with comprehensive profiles
  const dentist1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Dr. Michael Chen',
      phone: '+**********',
      password: hashedPassword,
      role: UserRole.DENTIST,
      status: UserStatus.ACTIVE,
      emailVerified: new Date(),
      image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
      dentistProfile: {
        create: {
          licenseNumber: 'DDS-12345',
          specialization: 'General Dentistry',
          yearsExperience: 8,
          education: 'DDS from University of California, San Francisco\nContinuing Education in Cosmetic Dentistry',
          certifications: 'Board Certified in General Dentistry\nCertified in Invisalign Treatment\nAdvanced Implant Dentistry Certificate',
          bio: 'Dr. Chen specializes in comprehensive dental care with a focus on preventive dentistry and patient comfort. He has been serving the community for over 8 years and is passionate about helping patients achieve optimal oral health through gentle, personalized care.',
          consultationFee: 1200.00, // ₱1,200
          isAvailable: true,
        },
      },
    },
    include: {
      dentistProfile: true,
    },
  })

  const dentist2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Dr. Emily Davis',
      phone: '+**********',
      password: hashedPassword,
      role: UserRole.DENTIST,
      status: UserStatus.ACTIVE,
      emailVerified: new Date(),
      image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=400&fit=crop&crop=face',
      dentistProfile: {
        create: {
          licenseNumber: 'DDS-67890',
          specialization: 'Orthodontics',
          yearsExperience: 12,
          education: 'DDS from Harvard School of Dental Medicine\nOrthodontics Residency at Boston University\nMaster of Science in Orthodontics',
          certifications: 'Board Certified Orthodontist\nInvisalign Elite Provider\nCertified in Damon System Braces\nTMJ Disorder Treatment Specialist',
          bio: 'Dr. Davis is an experienced orthodontist specializing in braces and Invisalign treatments. With over 12 years of experience, she has helped thousands of patients achieve beautiful, healthy smiles using the latest orthodontic techniques and technology.',
          consultationFee: 2000.00, // ₱2,000
          isAvailable: true,
        },
      },
    },
    include: {
      dentistProfile: true,
    },
  })

  const dentist3 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Dr. Robert Martinez',
      phone: '+**********',
      password: hashedPassword,
      role: UserRole.DENTIST,
      status: UserStatus.ACTIVE,
      emailVerified: new Date(),
      image: 'https://images.unsplash.com/photo-1622253692010-333f2da6031d?w=400&h=400&fit=crop&crop=face',
      dentistProfile: {
        create: {
          licenseNumber: 'DDS-54321',
          specialization: 'Oral Surgery',
          yearsExperience: 15,
          education: 'DDS from University of Michigan\nOral and Maxillofacial Surgery Residency\nFellowship in Dental Implantology',
          certifications: 'Board Certified Oral and Maxillofacial Surgeon\nFellow of American Association of Oral and Maxillofacial Surgeons\nCertified in IV Sedation',
          bio: 'Dr. Martinez is a skilled oral surgeon with extensive experience in dental implants, wisdom tooth extraction, and complex oral surgeries. He is committed to providing safe, comfortable surgical care using advanced techniques and sedation options.',
          consultationFee: 2500.00, // ₱2,500
          isAvailable: true,
        },
      },
    },
    include: {
      dentistProfile: true,
    },
  })

  // Create Google OAuth accounts for dentists
  await prisma.account.create({
    data: {
      userId: dentist1.id,
      type: 'oauth',
      provider: 'google',
      providerAccountId: 'google_dentist1_456',
      access_token: 'mock_access_token_dentist1',
      token_type: 'Bearer',
      scope: 'openid email profile',
    },
  })

  // Create comprehensive Patient Users with Google OAuth compatibility
  const patients = await Promise.all([
    // Patient 1 - Regular credentials user
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'John Smith',
        phone: '+**********',
        password: hashedPassword,
        role: UserRole.PATIENT,
        status: UserStatus.ACTIVE,
        emailVerified: new Date(),
        image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
        patientProfile: {
          create: {
            dateOfBirth: new Date('1985-06-15'),
            gender: 'Male',
            address: '123 Main St, Anytown, ST 12345',
            emergencyContact: 'Jane Smith',
            emergencyPhone: '+**********',
            insuranceProvider: 'Blue Cross Blue Shield',
            insuranceNumber: 'BC123456789',
            medicalHistory: 'No significant medical history. Regular dental checkups since childhood.',
            allergies: 'None known',
            medications: 'None',
          },
        },
      },
      include: {
        patientProfile: true,
      },
    }),

    // Patient 2 - Google OAuth user
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Alice Johnson',
        phone: '+**********',
        role: UserRole.PATIENT,
        status: UserStatus.ACTIVE,
        emailVerified: new Date(),
        image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
        patientProfile: {
          create: {
            dateOfBirth: new Date('1992-03-22'),
            gender: 'Female',
            address: '456 Oak Ave, Somewhere, ST 67890',
            emergencyContact: 'Bob Johnson',
            emergencyPhone: '+**********',
            insuranceProvider: 'Aetna',
            insuranceNumber: 'AET987654321',
            medicalHistory: 'Hypertension, controlled with medication. Previous orthodontic treatment completed in 2015.',
            allergies: 'Penicillin',
            medications: 'Lisinopril 10mg daily',
          },
        },
      },
      include: {
        patientProfile: true,
      },
    }),

    // Patient 3 - Google OAuth user with dental history
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'David Wilson',
        phone: '+**********',
        role: UserRole.PATIENT,
        status: UserStatus.ACTIVE,
        emailVerified: new Date(),
        image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
        patientProfile: {
          create: {
            dateOfBirth: new Date('1978-11-08'),
            gender: 'Male',
            address: '789 Pine St, Elsewhere, ST 54321',
            emergencyContact: 'Sarah Wilson',
            emergencyPhone: '+**********',
            insuranceProvider: 'Cigna',
            insuranceNumber: 'CIG456789123',
            medicalHistory: 'Diabetes Type 2, well controlled. History of periodontal disease, currently stable.',
            allergies: 'Latex, Codeine',
            medications: 'Metformin 500mg twice daily, Lisinopril 5mg daily',
          },
        },
      },
      include: {
        patientProfile: true,
      },
    }),

    // Patient 4 - Young patient with orthodontic needs
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Emma Brown',
        phone: '+**********',
        role: UserRole.PATIENT,
        status: UserStatus.ACTIVE,
        emailVerified: new Date(),
        image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
        patientProfile: {
          create: {
            dateOfBirth: new Date('2006-09-12'),
            gender: 'Female',
            address: '321 Elm St, Newtown, ST 98765',
            emergencyContact: 'Lisa Brown',
            emergencyPhone: '+**********',
            insuranceProvider: 'Delta Dental',
            insuranceNumber: 'DD789123456',
            medicalHistory: 'No significant medical history. Excellent oral hygiene habits.',
            allergies: 'None known',
            medications: 'None',
          },
        },
      },
      include: {
        patientProfile: true,
      },
    }),

    // Patient 5 - Senior patient with complex dental needs
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Robert Taylor',
        phone: '+**********',
        role: UserRole.PATIENT,
        status: UserStatus.ACTIVE,
        emailVerified: new Date(),
        image: 'https://images.unsplash.com/photo-**********-0b93528c311a?w=400&h=400&fit=crop&crop=face',
        patientProfile: {
          create: {
            dateOfBirth: new Date('1955-04-30'),
            gender: 'Male',
            address: '654 Maple Ave, Oldtown, ST 13579',
            emergencyContact: 'Margaret Taylor',
            emergencyPhone: '+**********',
            insuranceProvider: 'Medicare + AARP Dental',
            insuranceNumber: 'MED123456789',
            medicalHistory: 'Hypertension, high cholesterol, previous heart attack (2018). Multiple dental restorations, partial denture upper left.',
            allergies: 'Aspirin, Iodine',
            medications: 'Atorvastatin 20mg daily, Metoprolol 50mg twice daily, Clopidogrel 75mg daily',
          },
        },
      },
      include: {
        patientProfile: true,
      },
    }),
  ])

  // Create Google OAuth accounts for patients
  await Promise.all([
    prisma.account.create({
      data: {
        userId: patients[1].id, // Alice Johnson
        type: 'oauth',
        provider: 'google',
        providerAccountId: 'google_alice_789',
        access_token: 'mock_access_token_alice',
        token_type: 'Bearer',
        scope: 'openid email profile',
      },
    }),
    prisma.account.create({
      data: {
        userId: patients[2].id, // David Wilson
        type: 'oauth',
        provider: 'google',
        providerAccountId: 'google_david_101',
        access_token: 'mock_access_token_david',
        token_type: 'Bearer',
        scope: 'openid email profile',
      },
    }),
    prisma.account.create({
      data: {
        userId: patients[3].id, // Emma Brown
        type: 'oauth',
        provider: 'google',
        providerAccountId: 'google_emma_202',
        access_token: 'mock_access_token_emma',
        token_type: 'Bearer',
        scope: 'openid email profile',
      },
    }),
    prisma.account.create({
      data: {
        userId: patients[4].id, // Robert Taylor
        type: 'oauth',
        provider: 'google',
        providerAccountId: 'google_robert_303',
        access_token: 'mock_access_token_robert',
        token_type: 'Bearer',
        scope: 'openid email profile',
      },
    }),
  ])

  console.log('🏢 Creating clinic branches...')

  // Create Main Branch
  const mainBranch = await prisma.branch.create({
    data: {
      name: 'Bright Smile Dental - Main Branch',
      address: '123 Dental St, Downtown, CA 90210',
      phone: '(*************',
      email: '<EMAIL>',
      description: 'Our flagship dental clinic offering comprehensive dental care services with state-of-the-art equipment and experienced professionals.',
      isActive: true,
    },
  })

  // Create Secondary Branch
  const secondaryBranch = await prisma.branch.create({
    data: {
      name: 'Bright Smile Dental - Westside Branch',
      address: '456 Health Ave, Westside, CA 90211',
      phone: '(*************',
      email: '<EMAIL>',
      description: 'Our modern westside location specializing in family dentistry and orthodontic services.',
      isActive: true,
    },
  })

  console.log('🦷 Creating dental services...')

  // Create comprehensive dental services
  const services = await Promise.all([
    // General Services
    prisma.service.create({
      data: {
        name: 'General Consultation',
        description: 'Comprehensive dental examination including oral cancer screening, bite analysis, and treatment planning',
        category: 'General',
        duration: 60,
        price: 1200.00, // ₱1,200 (mid-range for consultation)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Emergency Consultation',
        description: 'Urgent dental care for pain relief and emergency treatment',
        category: 'General',
        duration: 45,
        price: 1500.00, // ₱1,500 (emergency premium)
        isActive: true,
      },
    }),

    // Preventive Services
    prisma.service.create({
      data: {
        name: 'Dental Cleaning (Regular)',
        description: 'Professional teeth cleaning and polishing with fluoride treatment',
        category: 'Preventive',
        duration: 45,
        price: 1500.00, // ₱1,500 (mid-range for cleaning)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Deep Cleaning (Scaling & Root Planing)',
        description: 'Deep cleaning procedure for patients with gum disease',
        category: 'Preventive',
        duration: 90,
        price: 3500.00, // ₱3,500 (specialized deep cleaning)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Fluoride Treatment',
        description: 'Professional fluoride application for cavity prevention',
        category: 'Preventive',
        duration: 15,
        price: 800.00, // ₱800 (basic preventive treatment)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Dental Sealants',
        description: 'Protective sealants for molars to prevent cavities',
        category: 'Preventive',
        duration: 30,
        price: 1200.00, // ₱1,200 (per tooth sealant)
        isActive: true,
      },
    }),

    // Restorative Services
    prisma.service.create({
      data: {
        name: 'Composite Filling',
        description: 'Tooth-colored composite resin filling for cavities',
        category: 'Restorative',
        duration: 60,
        price: 3000.00, // ₱3,000 (mid-range for composite filling)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Amalgam Filling',
        description: 'Silver amalgam filling for posterior teeth',
        category: 'Restorative',
        duration: 45,
        price: 2000.00, // ₱2,000 (lower cost for amalgam)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Dental Crown',
        description: 'Porcelain or ceramic crown to restore damaged tooth',
        category: 'Restorative',
        duration: 120,
        price: 20000.00, // ₱20,000 (mid-range for crown)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Dental Bridge',
        description: 'Fixed bridge to replace missing teeth',
        category: 'Restorative',
        duration: 150,
        price: 45000.00, // ₱45,000 (3-unit bridge)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Dental Implant',
        description: 'Titanium implant with crown to replace missing tooth',
        category: 'Restorative',
        duration: 180,
        price: 80000.00, // ₱80,000 (premium implant with crown)
        isActive: true,
      },
    }),

    // Endodontic Services
    prisma.service.create({
      data: {
        name: 'Root Canal Treatment',
        description: 'Endodontic treatment to save infected or damaged tooth',
        category: 'Endodontics',
        duration: 120,
        price: 12000.00, // ₱12,000 (mid-range for root canal)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Root Canal Retreatment',
        description: 'Retreatment of previously treated root canal',
        category: 'Endodontics',
        duration: 150,
        price: 15000.00, // ₱15,000 (higher for retreatment)
        isActive: true,
      },
    }),

    // Orthodontic Services
    prisma.service.create({
      data: {
        name: 'Orthodontic Consultation',
        description: 'Initial consultation and treatment planning for braces or aligners',
        category: 'Orthodontics',
        duration: 60,
        price: 2000.00, // ₱2,000 (mid-range for ortho consultation)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Traditional Braces',
        description: 'Metal braces treatment (full treatment cost)',
        category: 'Orthodontics',
        duration: 90,
        price: 65000.00, // ₱65,000 (mid-range for full braces treatment)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Invisalign Treatment',
        description: 'Clear aligner treatment (full treatment cost)',
        category: 'Orthodontics',
        duration: 90,
        price: 120000.00, // ₱120,000 (premium for Invisalign)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Orthodontic Adjustment',
        description: 'Regular adjustment appointment for braces',
        category: 'Orthodontics',
        duration: 30,
        price: 1500.00, // ₱1,500 (monthly adjustment fee)
        isActive: true,
      },
    }),

    // Oral Surgery Services
    prisma.service.create({
      data: {
        name: 'Tooth Extraction (Simple)',
        description: 'Simple tooth extraction procedure',
        category: 'Oral Surgery',
        duration: 30,
        price: 2000.00, // ₱2,000 (mid-range for simple extraction)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Tooth Extraction (Surgical)',
        description: 'Surgical extraction for impacted or complex cases',
        category: 'Oral Surgery',
        duration: 60,
        price: 5000.00, // ₱5,000 (higher for surgical extraction)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Wisdom Tooth Extraction',
        description: 'Extraction of wisdom teeth (per tooth)',
        category: 'Oral Surgery',
        duration: 45,
        price: 4000.00, // ₱4,000 (specialized wisdom tooth extraction)
        isActive: true,
      },
    }),

    // Cosmetic Services
    prisma.service.create({
      data: {
        name: 'Teeth Whitening (In-Office)',
        description: 'Professional teeth whitening treatment in clinic',
        category: 'Cosmetic',
        duration: 90,
        price: 12000.00, // ₱12,000 (mid-range for professional whitening)
        isActive: true,
      },
    }),
    prisma.service.create({
      data: {
        name: 'Porcelain Veneers',
        description: 'Custom porcelain veneers for smile enhancement (per tooth)',
        category: 'Cosmetic',
        duration: 120,
        price: 25000.00, // ₱25,000 (premium per tooth veneer)
        isActive: true,
      },
    }),
  ])

  console.log('🔗 Creating branch-service and branch-dentist relationships...')

  // Associate all services with both branches
  const branchServices = await Promise.all([
    ...services.map(service =>
      prisma.branchService.create({
        data: {
          branchId: mainBranch.id,
          serviceId: service.id,
          price: service.price, // Use the service's base price
        },
      })
    ),
    ...services.map(service =>
      prisma.branchService.create({
        data: {
          branchId: secondaryBranch.id,
          serviceId: service.id,
          price: service.price, // Use the service's base price
        },
      })
    ),
  ])

  // Associate dentists with branches
  const branchDentists = await Promise.all([
    // Main branch dentists
    prisma.branchDentist.create({
      data: {
        branchId: mainBranch.id,
        dentistId: dentist1.dentistProfile!.id,
      },
    }),
    prisma.branchDentist.create({
      data: {
        branchId: mainBranch.id,
        dentistId: dentist2.dentistProfile!.id,
      },
    }),
    prisma.branchDentist.create({
      data: {
        branchId: mainBranch.id,
        dentistId: dentist3.dentistProfile!.id,
      },
    }),
    // Secondary branch dentists
    prisma.branchDentist.create({
      data: {
        branchId: secondaryBranch.id,
        dentistId: dentist1.dentistProfile!.id,
      },
    }),
    prisma.branchDentist.create({
      data: {
        branchId: secondaryBranch.id,
        dentistId: dentist2.dentistProfile!.id,
      },
    }),
  ])

  // Associate staff with branches
  const branchStaff = await Promise.all([
    prisma.branchStaff.create({
      data: {
        branchId: mainBranch.id,
        staffId: staff1.staffProfile!.id,
      },
    }),
    prisma.branchStaff.create({
      data: {
        branchId: mainBranch.id,
        staffId: staff2.staffProfile!.id,
      },
    }),
    prisma.branchStaff.create({
      data: {
        branchId: secondaryBranch.id,
        staffId: staff2.staffProfile!.id,
      },
    }),
  ])

  console.log('📅 Creating dentist schedules...')

  // Create dentist schedules (Monday to Friday, 9 AM to 5 PM)
  const schedules = await Promise.all([
    // Dr. Chen's schedule (Monday to Friday)
    ...Array.from({ length: 5 }, (_, i) =>
      prisma.dentistSchedule.create({
        data: {
          dentistId: dentist1.dentistProfile!.id,
          dayOfWeek: i + 1, // Monday = 1, Friday = 5
          startTime: '09:00',
          endTime: '17:00',
          isActive: true,
        },
      })
    ),
    // Dr. Davis's schedule (Tuesday to Saturday)
    ...Array.from({ length: 5 }, (_, i) =>
      prisma.dentistSchedule.create({
        data: {
          dentistId: dentist2.dentistProfile!.id,
          dayOfWeek: i + 2, // Tuesday = 2, Saturday = 6
          startTime: '08:00',
          endTime: '16:00',
          isActive: true,
        },
      })
    ),
    // Dr. Martinez's schedule (Monday, Wednesday, Friday)
    prisma.dentistSchedule.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        dayOfWeek: 1, // Monday
        startTime: '10:00',
        endTime: '18:00',
        isActive: true,
      },
    }),
    prisma.dentistSchedule.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        dayOfWeek: 3, // Wednesday
        startTime: '10:00',
        endTime: '18:00',
        isActive: true,
      },
    }),
    prisma.dentistSchedule.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        dayOfWeek: 5, // Friday
        startTime: '10:00',
        endTime: '18:00',
        isActive: true,
      },
    }),
  ])

  console.log('⏰ Creating comprehensive working hours with breaks...')

  // Helper function to create Philippine timezone dates
  const createPhilippineDate = (dateString: string): Date => {
    // Create date in Philippine timezone (UTC+8)
    const date = new Date(dateString)
    return new Date(date.getTime() + (8 * 60 * 60 * 1000)) // Add 8 hours for UTC+8
  }

  // Create detailed working hours for each dentist with lunch breaks and buffer times
  const workingHours = await Promise.all([
    // Dr. Chen's working hours (Monday to Friday, 8:00 AM - 5:00 PM with lunch)
    ...Array.from({ length: 5 }, (_, i) =>
      prisma.dentistWorkingHours.create({
        data: {
          dentistId: dentist1.dentistProfile!.id,
          dayOfWeek: i + 1, // Monday = 1, Friday = 5
          startTime: '08:00',
          endTime: '17:00',
          lunchStart: '12:00',
          lunchEnd: '13:00',
          breakSlots: [
            { startTime: '10:00', endTime: '10:15', type: 'break' },
            { startTime: '15:00', endTime: '15:15', type: 'break' }
          ],
          isActive: true,
          effectiveFrom: createPhilippineDate('2024-01-01T00:00:00Z'),
        },
      })
    ),

    // Dr. Davis's working hours (Tuesday to Friday, 8:00 AM - 4:00 PM)
    ...Array.from({ length: 4 }, (_, i) =>
      prisma.dentistWorkingHours.create({
        data: {
          dentistId: dentist2.dentistProfile!.id,
          dayOfWeek: i + 2, // Tuesday = 2, Friday = 5
          startTime: '08:00',
          endTime: '16:00',
          lunchStart: '12:00',
          lunchEnd: '13:00',
          breakSlots: [
            { startTime: '10:30', endTime: '10:45', type: 'break' },
            { startTime: '14:30', endTime: '14:45', type: 'break' }
          ],
          isActive: true,
          effectiveFrom: createPhilippineDate('2024-01-01T00:00:00Z'),
        },
      })
    ),

    // Dr. Martinez's working hours (Monday, Wednesday, Friday - longer days)
    prisma.dentistWorkingHours.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '18:00',
        lunchStart: '13:00',
        lunchEnd: '14:00',
        breakSlots: [
          { startTime: '11:00', endTime: '11:15', type: 'break' },
          { startTime: '16:00', endTime: '16:15', type: 'break' }
        ],
        isActive: true,
        effectiveFrom: createPhilippineDate('2024-01-01T00:00:00Z'),
      },
    }),
    prisma.dentistWorkingHours.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        dayOfWeek: 3, // Wednesday
        startTime: '09:00',
        endTime: '18:00',
        lunchStart: '13:00',
        lunchEnd: '14:00',
        breakSlots: [
          { startTime: '11:00', endTime: '11:15', type: 'break' },
          { startTime: '16:00', endTime: '16:15', type: 'break' }
        ],
        isActive: true,
        effectiveFrom: createPhilippineDate('2024-01-01T00:00:00Z'),
      },
    }),
    prisma.dentistWorkingHours.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        dayOfWeek: 5, // Friday
        startTime: '09:00',
        endTime: '18:00',
        lunchStart: '13:00',
        lunchEnd: '14:00',
        breakSlots: [
          { startTime: '11:00', endTime: '11:15', type: 'break' },
          { startTime: '16:00', endTime: '16:15', type: 'break' }
        ],
        isActive: true,
        effectiveFrom: createPhilippineDate('2024-01-01T00:00:00Z'),
      },
    }),

    // Saturday working hours for Dr. Davis (shorter day)
    prisma.dentistWorkingHours.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        dayOfWeek: 6, // Saturday
        startTime: '09:00',
        endTime: '14:00',
        lunchStart: '12:00',
        lunchEnd: '12:30',
        breakSlots: [
          { startTime: '10:30', endTime: '10:45', type: 'break' }
        ],
        isActive: true,
        effectiveFrom: createPhilippineDate('2024-01-01T00:00:00Z'),
      },
    }),
  ])

  console.log('🔲 Creating time blocks for specialized procedures...')

  // Create time blocks for different types of procedures and administrative tasks
  const timeBlocks = await Promise.all([
    // Dr. Chen's time blocks - General dentistry focus
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        title: 'Root Canal Procedures',
        description: 'Reserved time for complex root canal treatments',
        startTime: createPhilippineDate('2024-07-08T09:00:00Z'), // Monday morning
        endTime: createPhilippineDate('2024-07-08T11:00:00Z'),
        blockType: TimeBlockType.PROCEDURE,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'weekly',
          daysOfWeek: [1], // Monday
          endDate: '2024-12-31'
        }),
        color: '#3B82F6', // Blue
        isActive: true,
      },
    }),
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        title: 'Administrative Tasks',
        description: 'Patient records review and treatment planning',
        startTime: createPhilippineDate('2024-07-08T16:00:00Z'),
        endTime: createPhilippineDate('2024-07-08T17:00:00Z'),
        blockType: TimeBlockType.ADMINISTRATIVE,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'daily',
          daysOfWeek: [1, 2, 3, 4, 5], // Weekdays
          endDate: '2024-12-31'
        }),
        color: '#F59E0B', // Amber
        isActive: true,
      },
    }),

    // Dr. Davis's time blocks - Oral surgery focus
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        title: 'Surgical Procedures',
        description: 'Complex extractions and oral surgery',
        startTime: createPhilippineDate('2024-07-09T08:00:00Z'), // Tuesday morning
        endTime: createPhilippineDate('2024-07-09T10:00:00Z'),
        blockType: TimeBlockType.SURGERY,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'weekly',
          daysOfWeek: [2, 4], // Tuesday and Thursday
          endDate: '2024-12-31'
        }),
        color: '#DC2626', // Red
        isActive: true,
      },
    }),
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        title: 'Emergency Buffer',
        description: 'Reserved time for emergency cases',
        startTime: createPhilippineDate('2024-07-09T14:00:00Z'),
        endTime: createPhilippineDate('2024-07-09T15:00:00Z'),
        blockType: TimeBlockType.EMERGENCY_RESERVED,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'daily',
          daysOfWeek: [2, 3, 4, 5, 6], // Tuesday to Saturday
          endDate: '2024-12-31'
        }),
        color: '#EF4444', // Red
        isActive: true,
      },
    }),

    // Dr. Martinez's time blocks - Orthodontics focus
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        title: 'Orthodontic Consultations',
        description: 'Initial consultations and treatment planning',
        startTime: createPhilippineDate('2024-07-08T14:00:00Z'), // Monday afternoon
        endTime: createPhilippineDate('2024-07-08T16:00:00Z'),
        blockType: TimeBlockType.PROCEDURE,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'weekly',
          daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
          endDate: '2024-12-31'
        }),
        color: '#8B5CF6', // Purple
        isActive: true,
      },
    }),
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        title: 'Personal Development',
        description: 'Continuing education and research',
        startTime: createPhilippineDate('2024-07-10T17:00:00Z'), // Wednesday evening
        endTime: createPhilippineDate('2024-07-10T18:00:00Z'),
        blockType: TimeBlockType.PERSONAL,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'weekly',
          daysOfWeek: [3], // Wednesday
          endDate: '2024-12-31'
        }),
        color: '#10B981', // Green
        isActive: true,
      },
    }),

    // Lunch blocks for all dentists
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        title: 'Lunch Break',
        description: 'Daily lunch break',
        startTime: createPhilippineDate('2024-07-08T12:00:00Z'),
        endTime: createPhilippineDate('2024-07-08T13:00:00Z'),
        blockType: TimeBlockType.LUNCH,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'daily',
          daysOfWeek: [1, 2, 3, 4, 5], // Weekdays
          endDate: '2024-12-31'
        }),
        color: '#6B7280', // Gray
        isActive: true,
      },
    }),
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        title: 'Lunch Break',
        description: 'Daily lunch break',
        startTime: createPhilippineDate('2024-07-09T12:00:00Z'),
        endTime: createPhilippineDate('2024-07-09T13:00:00Z'),
        blockType: TimeBlockType.LUNCH,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'daily',
          daysOfWeek: [2, 3, 4, 5, 6], // Tuesday to Saturday
          endDate: '2024-12-31'
        }),
        color: '#6B7280', // Gray
        isActive: true,
      },
    }),
    prisma.dentistTimeBlock.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        title: 'Lunch Break',
        description: 'Daily lunch break',
        startTime: createPhilippineDate('2024-07-08T13:00:00Z'),
        endTime: createPhilippineDate('2024-07-08T14:00:00Z'),
        blockType: TimeBlockType.LUNCH,
        isRecurring: true,
        recurringPattern: JSON.stringify({
          frequency: 'weekly',
          daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
          endDate: '2024-12-31'
        }),
        color: '#6B7280', // Gray
        isActive: true,
      },
    }),
  ])

  console.log('🏖️ Creating time-off records and vacation schedules...')

  // Create time-off records for dentists
  const timeOffRecords = await Promise.all([
    // Dr. Chen's vacation
    prisma.dentistTimeOff.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        title: 'Summer Vacation',
        startDate: createPhilippineDate('2024-08-15T00:00:00Z'),
        endDate: createPhilippineDate('2024-08-25T23:59:59Z'),
        timeOffType: TimeOffType.VACATION,
        status: TimeOffStatus.APPROVED,
        reason: 'Annual family vacation',
        isAllDay: true,
        approvedBy: admin.id,
        approvedAt: createPhilippineDate('2024-07-01T10:00:00Z'),
        notes: 'Dr. Martinez will cover emergency cases',
      },
    }),

    // Dr. Davis's continuing education
    prisma.dentistTimeOff.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        title: 'Oral Surgery Conference',
        startDate: createPhilippineDate('2024-09-10T00:00:00Z'),
        endDate: createPhilippineDate('2024-09-12T23:59:59Z'),
        timeOffType: TimeOffType.CONFERENCE,
        status: TimeOffStatus.APPROVED,
        reason: 'International Oral Surgery Conference in Manila',
        isAllDay: true,
        approvedBy: admin.id,
        approvedAt: createPhilippineDate('2024-08-01T14:00:00Z'),
        notes: 'Clinic will reschedule surgical procedures',
      },
    }),

    // Dr. Martinez's personal day
    prisma.dentistTimeOff.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        title: 'Personal Day',
        startDate: createPhilippineDate('2024-07-15T00:00:00Z'),
        endDate: createPhilippineDate('2024-07-15T23:59:59Z'),
        timeOffType: TimeOffType.PERSONAL,
        status: TimeOffStatus.APPROVED,
        reason: 'Family commitment',
        isAllDay: true,
        approvedBy: admin.id,
        approvedAt: createPhilippineDate('2024-07-01T09:00:00Z'),
        notes: 'Appointments rescheduled to following week',
      },
    }),

    // Pending time-off request
    prisma.dentistTimeOff.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        title: 'Christmas Holiday',
        startDate: createPhilippineDate('2024-12-23T00:00:00Z'),
        endDate: createPhilippineDate('2024-12-27T23:59:59Z'),
        timeOffType: TimeOffType.VACATION,
        status: TimeOffStatus.PENDING,
        reason: 'Christmas holiday with family',
        isAllDay: true,
        notes: 'Requesting extended holiday break',
      },
    }),

    // Sick leave example
    prisma.dentistTimeOff.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        title: 'Sick Leave',
        startDate: createPhilippineDate('2024-07-20T00:00:00Z'),
        endDate: createPhilippineDate('2024-07-21T23:59:59Z'),
        timeOffType: TimeOffType.SICK_LEAVE,
        status: TimeOffStatus.APPROVED,
        reason: 'Flu symptoms',
        isAllDay: true,
        approvedBy: admin.id,
        approvedAt: createPhilippineDate('2024-07-20T08:00:00Z'),
        notes: 'Medical certificate provided',
      },
    }),
  ])

  console.log('📋 Creating schedule templates for recurring patterns...')

  // Create schedule templates for different working patterns
  const scheduleTemplates = await Promise.all([
    // Standard weekday template
    prisma.dentistScheduleTemplate.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        name: 'Standard Weekday Schedule',
        description: 'Regular Monday-Friday working hours with breaks',
        templateData: {
          workingHours: [
            { dayOfWeek: 1, startTime: '08:00', endTime: '17:00', lunchStart: '12:00', lunchEnd: '13:00' },
            { dayOfWeek: 2, startTime: '08:00', endTime: '17:00', lunchStart: '12:00', lunchEnd: '13:00' },
            { dayOfWeek: 3, startTime: '08:00', endTime: '17:00', lunchStart: '12:00', lunchEnd: '13:00' },
            { dayOfWeek: 4, startTime: '08:00', endTime: '17:00', lunchStart: '12:00', lunchEnd: '13:00' },
            { dayOfWeek: 5, startTime: '08:00', endTime: '17:00', lunchStart: '12:00', lunchEnd: '13:00' }
          ],
          timeBlocks: [
            { title: 'Morning Break', startTime: '10:00', endTime: '10:15', type: 'break' },
            { title: 'Afternoon Break', startTime: '15:00', endTime: '15:15', type: 'break' }
          ]
        },
        isDefault: true,
        isActive: true,
      },
    }),

    // Extended hours template
    prisma.dentistScheduleTemplate.create({
      data: {
        dentistId: dentist3.dentistProfile!.id,
        name: 'Extended Hours Template',
        description: 'Longer working days for specialized procedures',
        templateData: {
          workingHours: [
            { dayOfWeek: 1, startTime: '09:00', endTime: '18:00', lunchStart: '13:00', lunchEnd: '14:00' },
            { dayOfWeek: 3, startTime: '09:00', endTime: '18:00', lunchStart: '13:00', lunchEnd: '14:00' },
            { dayOfWeek: 5, startTime: '09:00', endTime: '18:00', lunchStart: '13:00', lunchEnd: '14:00' }
          ],
          timeBlocks: [
            { title: 'Consultation Block', startTime: '14:00', endTime: '16:00', type: 'procedure' },
            { title: 'Admin Time', startTime: '17:00', endTime: '18:00', type: 'admin' }
          ]
        },
        isDefault: false,
        isActive: true,
      },
    }),

    // Weekend/Saturday template
    prisma.dentistScheduleTemplate.create({
      data: {
        dentistId: dentist2.dentistProfile!.id,
        name: 'Weekend Schedule',
        description: 'Saturday working hours with shorter day',
        templateData: {
          workingHours: [
            { dayOfWeek: 6, startTime: '09:00', endTime: '14:00', lunchStart: '12:00', lunchEnd: '12:30' }
          ],
          timeBlocks: [
            { title: 'Emergency Slots', startTime: '09:00', endTime: '11:00', type: 'emergency' },
            { title: 'Follow-ups', startTime: '13:00', endTime: '14:00', type: 'procedure' }
          ]
        },
        isDefault: false,
        isActive: true,
      },
    }),

    // Holiday schedule template
    prisma.dentistScheduleTemplate.create({
      data: {
        dentistId: dentist1.dentistProfile!.id,
        name: 'Holiday Schedule',
        description: 'Reduced hours during holiday periods',
        templateData: {
          workingHours: [
            { dayOfWeek: 1, startTime: '10:00', endTime: '15:00', lunchStart: '12:30', lunchEnd: '13:00' },
            { dayOfWeek: 2, startTime: '10:00', endTime: '15:00', lunchStart: '12:30', lunchEnd: '13:00' },
            { dayOfWeek: 3, startTime: '10:00', endTime: '15:00', lunchStart: '12:30', lunchEnd: '13:00' }
          ],
          timeBlocks: [
            { title: 'Emergency Only', startTime: '10:00', endTime: '15:00', type: 'emergency' }
          ]
        },
        isDefault: false,
        isActive: false, // Inactive until needed
      },
    }),
  ])

  console.log('🦷 Creating dental charts and medical records...')

  // Create dental charts for patients
  const dentalCharts = await Promise.all(
    patients.map(patient =>
      prisma.dentalChart.create({
        data: {
          patientId: patient.patientProfile!.id,
        },
      })
    )
  )

  // Create some tooth records for patients with dental history
  const toothRecords = await Promise.all([
    // John Smith - some fillings
    prisma.toothRecord.create({
      data: {
        dentalChartId: dentalCharts[0].id,
        toothNumber: 14,
        condition: ToothCondition.FILLED,
        notes: 'Composite filling placed 2022',
      },
    }),
    prisma.toothRecord.create({
      data: {
        dentalChartId: dentalCharts[0].id,
        toothNumber: 19,
        condition: ToothCondition.FILLED,
        notes: 'Amalgam filling, good condition',
      },
    }),

    // David Wilson - more complex history
    prisma.toothRecord.create({
      data: {
        dentalChartId: dentalCharts[2].id,
        toothNumber: 30,
        condition: ToothCondition.CROWN,
        notes: 'Porcelain crown due to large cavity',
      },
    }),
    prisma.toothRecord.create({
      data: {
        dentalChartId: dentalCharts[2].id,
        toothNumber: 18,
        condition: ToothCondition.MISSING,
        notes: 'Extracted due to severe decay',
      },
    }),

    // Robert Taylor - senior patient with multiple restorations
    prisma.toothRecord.create({
      data: {
        dentalChartId: dentalCharts[4].id,
        toothNumber: 3,
        condition: ToothCondition.CROWN,
        notes: 'Gold crown, excellent condition',
      },
    }),
    prisma.toothRecord.create({
      data: {
        dentalChartId: dentalCharts[4].id,
        toothNumber: 12,
        condition: ToothCondition.MISSING,
        notes: 'Missing, partial denture in place',
      },
    }),
  ])

  console.log('📅 Creating sample appointments...')

  // Get date ranges for appointments
  const today = new Date()
  const pastDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
  const futureDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days from now

  // Create sample appointments with various statuses
  const appointments = await Promise.all([
    // Past completed appointments
    prisma.appointment.create({
      data: {
        patientId: patients[0].patientProfile!.id,
        dentistId: dentist1.dentistProfile!.id,
        branchId: mainBranch.id,
        serviceId: services[2].id, // Dental Cleaning
        scheduledAt: new Date('2024-06-15T10:00:00Z'),
        duration: 45,
        status: AppointmentStatus.COMPLETED,
        type: AppointmentType.CLEANING,
        notes: 'Regular cleaning completed. Good oral hygiene.',
        symptoms: 'None',
      },
    }),

    prisma.appointment.create({
      data: {
        patientId: patients[1].patientProfile!.id,
        dentistId: dentist2.dentistProfile!.id,
        branchId: mainBranch.id,
        serviceId: services[13].id, // Orthodontic Consultation
        scheduledAt: new Date('2024-06-20T14:00:00Z'),
        duration: 60,
        status: AppointmentStatus.COMPLETED,
        type: AppointmentType.CONSULTATION,
        notes: 'Orthodontic evaluation completed. Treatment plan discussed.',
        symptoms: 'Crowded teeth, bite issues',
      },
    }),

    // Upcoming scheduled appointments
    prisma.appointment.create({
      data: {
        patientId: patients[2].patientProfile!.id,
        dentistId: dentist1.dentistProfile!.id,
        branchId: mainBranch.id,
        serviceId: services[6].id, // Composite Filling
        scheduledAt: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        duration: 60,
        status: AppointmentStatus.SCHEDULED,
        type: AppointmentType.FILLING,
        notes: 'Filling needed for tooth #14',
        symptoms: 'Sensitivity to cold',
      },
    }),

    prisma.appointment.create({
      data: {
        patientId: patients[3].patientProfile!.id,
        dentistId: dentist2.dentistProfile!.id,
        branchId: secondaryBranch.id,
        serviceId: services[14].id, // Traditional Braces
        scheduledAt: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
        duration: 90,
        status: AppointmentStatus.CONFIRMED,
        type: AppointmentType.ORTHODONTICS,
        notes: 'Braces placement appointment',
        symptoms: 'None - orthodontic treatment',
      },
    }),

    // Emergency appointment
    prisma.appointment.create({
      data: {
        patientId: patients[4].patientProfile!.id,
        dentistId: dentist3.dentistProfile!.id,
        branchId: mainBranch.id,
        serviceId: services[1].id, // Emergency Consultation
        scheduledAt: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        duration: 45,
        status: AppointmentStatus.SCHEDULED,
        type: AppointmentType.EMERGENCY,
        notes: 'Emergency consultation for severe pain',
        symptoms: 'Severe toothache, swelling',
      },
    }),

    // Cancelled appointment
    prisma.appointment.create({
      data: {
        patientId: patients[0].patientProfile!.id,
        dentistId: dentist1.dentistProfile!.id,
        branchId: mainBranch.id,
        serviceId: services[0].id, // General Consultation
        scheduledAt: new Date('2024-06-25T09:00:00Z'),
        duration: 60,
        status: AppointmentStatus.CANCELLED,
        type: AppointmentType.CONSULTATION,
        notes: 'Patient cancelled due to scheduling conflict',
        cancelReason: 'Patient scheduling conflict',
      },
    }),
  ])

  console.log('🏥 Creating treatments and medical records...')

  // Create treatments for completed appointments
  const treatments = await Promise.all([
    prisma.treatment.create({
      data: {
        appointmentId: appointments[0].id,
        patientId: patients[0].patientProfile!.id,
        dentistId: dentist1.dentistProfile!.id,
        serviceId: services[2].id, // Dental Cleaning
        status: TreatmentStatus.COMPLETED,
        diagnosis: 'Healthy gums, mild plaque buildup',
        procedure: 'Professional cleaning and polishing',
        notes: 'Patient advised to floss daily. Next cleaning in 6 months.',
        startedAt: new Date('2024-06-15T10:00:00Z'),
        completedAt: new Date('2024-06-15T10:45:00Z'),
        cost: 1500.00, // ₱1,500 for dental cleaning
      },
    }),

    prisma.treatment.create({
      data: {
        appointmentId: appointments[1].id,
        patientId: patients[1].patientProfile!.id,
        dentistId: dentist2.dentistProfile!.id,
        serviceId: services[13].id, // Orthodontic Consultation
        status: TreatmentStatus.COMPLETED,
        diagnosis: 'Class II malocclusion, crowded anterior teeth',
        procedure: 'Invisalign treatment recommended, 18-month duration',
        notes: 'Patient interested in Invisalign. Treatment plan and cost discussed.',
        startedAt: new Date('2024-06-20T14:00:00Z'),
        completedAt: new Date('2024-06-20T15:00:00Z'),
        cost: 2000.00, // ₱2,000 for orthodontic consultation
      },
    }),
  ])

  console.log('💰 Creating invoices and payments...')

  // Create invoices for completed treatments
  const invoices = await Promise.all([
    prisma.invoice.create({
      data: {
        patientId: patients[0].patientProfile!.id,
        invoiceNumber: 'INV-2024-001',
        totalAmount: 1500.00,
        paidAmount: 1500.00,
        status: PaymentStatus.PAID,
        dueDate: new Date('2024-06-30'),
        issuedAt: new Date('2024-06-15'),
        paidAt: new Date('2024-06-15'),
        notes: 'Payment received at time of service',
      },
    }),

    prisma.invoice.create({
      data: {
        patientId: patients[1].patientProfile!.id,
        invoiceNumber: 'INV-2024-002',
        totalAmount: 2000.00,
        paidAmount: 2000.00,
        status: PaymentStatus.PAID,
        dueDate: new Date('2024-07-05'),
        issuedAt: new Date('2024-06-20'),
        paidAt: new Date('2024-06-20'),
        notes: 'Consultation fee paid by credit card',
      },
    }),

    // Pending invoice for upcoming treatment
    prisma.invoice.create({
      data: {
        patientId: patients[2].patientProfile!.id,
        invoiceNumber: 'INV-2024-003',
        totalAmount: 3000.00, // ₱3,000 for composite filling
        paidAmount: 0.00,
        status: PaymentStatus.PENDING,
        dueDate: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000),
        issuedAt: new Date(),
        notes: 'Invoice for upcoming composite filling',
      },
    }),

    // Large invoice for orthodontic treatment
    prisma.invoice.create({
      data: {
        patientId: patients[3].patientProfile!.id,
        invoiceNumber: 'INV-2024-004',
        totalAmount: 65000.00, // ₱65,000 for traditional braces
        paidAmount: 20000.00, // ₱20,000 down payment
        status: PaymentStatus.PARTIAL,
        dueDate: new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000),
        issuedAt: new Date(),
        notes: 'Traditional braces treatment - payment plan active',
      },
    }),
  ])

  // Create invoice items
  const invoiceItems = await Promise.all([
    // Invoice 1 items
    prisma.invoiceItem.create({
      data: {
        invoiceId: invoices[0].id,
        treatmentId: treatments[0].id,
        description: 'Dental Cleaning - Regular',
        quantity: 1,
        unitPrice: 1500.00,
        totalPrice: 1500.00,
      },
    }),

    // Invoice 2 items
    prisma.invoiceItem.create({
      data: {
        invoiceId: invoices[1].id,
        treatmentId: treatments[1].id,
        description: 'Orthodontic Consultation',
        quantity: 1,
        unitPrice: 3000.00,
        totalPrice: 3000.00,
      },
    }),

    // Invoice 3 items
    prisma.invoiceItem.create({
      data: {
        invoiceId: invoices[2].id,
        description: 'Composite Filling',
        quantity: 1,
        unitPrice: 2000.00,
        totalPrice: 2000.00,
      },
    }),

    // Invoice 4 items
    prisma.invoiceItem.create({
      data: {
        invoiceId: invoices[3].id,
        description: 'Traditional Braces - Full Treatment',
        quantity: 1,
        unitPrice: 65000.00,
        totalPrice: 65000.00,
      },
    }),
  ])

  // Create payment records
  const payments = await Promise.all([
    prisma.payment.create({
      data: {
        invoiceId: invoices[0].id,
        amount: 1500.00,
        method: PaymentMethod.CREDIT_CARD,
        transactionId: 'cc_**********',
        status: PaymentStatus.PAID,
        processedAt: new Date('2024-06-15T10:45:00Z'),
        notes: 'Visa ending in 4242',
      },
    }),

    prisma.payment.create({
      data: {
        invoiceId: invoices[1].id,
        amount: 2000.00,
        method: PaymentMethod.CREDIT_CARD,
        transactionId: 'cc_0987654321',
        status: PaymentStatus.PAID,
        processedAt: new Date('2024-06-20T15:00:00Z'),
        notes: 'Mastercard ending in 8888',
      },
    }),

    // Partial payment for orthodontic treatment
    prisma.payment.create({
      data: {
        invoiceId: invoices[3].id,
        amount: 20000.00, // ₱20,000 down payment for braces
        method: PaymentMethod.BANK_TRANSFER,
        transactionId: 'bt_1122334455',
        status: PaymentStatus.PAID,
        processedAt: new Date(),
        notes: 'Down payment for braces treatment',
      },
    }),
  ])

  console.log('🔔 Creating notifications...')

  // Create sample notifications
  const notifications = await Promise.all([
    prisma.notification.create({
      data: {
        userId: patients[2].id,
        title: 'Appointment Reminder',
        message: 'Your appointment for composite filling is scheduled for next week.',
        type: NotificationType.APPOINTMENT_REMINDER,
        channel: NotificationChannel.EMAIL,
        isRead: false,
        appointmentId: appointments[2].id,
      },
    }),

    prisma.notification.create({
      data: {
        userId: patients[3].id,
        title: 'Payment Due',
        message: 'Your next payment for orthodontic treatment is due in 30 days.',
        type: NotificationType.PAYMENT_DUE,
        channel: NotificationChannel.IN_APP,
        isRead: false,
      },
    }),

    prisma.notification.create({
      data: {
        userId: patients[0].id,
        title: 'Appointment Confirmed',
        message: 'Your dental cleaning appointment has been completed. Thank you for visiting!',
        type: NotificationType.APPOINTMENT_CONFIRMATION,
        channel: NotificationChannel.EMAIL,
        isRead: true,
        appointmentId: appointments[0].id,
      },
    }),
  ])

  console.log('📊 Creating audit logs...')

  // Create some audit logs for system activity
  const auditLogs = await Promise.all([
    prisma.auditLog.create({
      data: {
        userId: admin.id,
        action: 'USER_LOGIN',
        resource: 'User',
        resourceId: admin.id,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    }),

    prisma.auditLog.create({
      data: {
        userId: staff1.id,
        action: 'APPOINTMENT_CREATED',
        resource: 'Appointment',
        resourceId: appointments[2].id,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    }),

    prisma.auditLog.create({
      data: {
        userId: dentist1.id,
        action: 'TREATMENT_COMPLETED',
        resource: 'Treatment',
        resourceId: treatments[0].id,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    }),
  ])

  console.log('✅ Database seeded successfully!')
  console.log(`\n📊 Summary of created data:`)
  console.log(`👥 Users:`)
  console.log(`  - 1 Admin user (<EMAIL>)`)
  console.log(`  - 2 Staff users (<EMAIL>, <EMAIL>)`)
  console.log(`  - 3 Dentist users (<EMAIL>, <EMAIL>, <EMAIL>)`)
  console.log(`  - 5 Patient users (with Google OAuth compatibility)`)
  console.log(`🏢 Clinic Infrastructure:`)
  console.log(`  - 2 Branches (Main and Westside)`)
  console.log(`  - ${services.length} Dental services`)
  console.log(`  - ${schedules.length} Dentist schedules`)
  console.log(`📅 Appointments & Treatments:`)
  console.log(`  - ${appointments.length} Sample appointments (various statuses)`)
  console.log(`  - ${treatments.length} Treatment records`)
  console.log(`  - ${dentalCharts.length} Dental charts with tooth records`)
  console.log(`💰 Financial Records:`)
  console.log(`  - ${invoices.length} Invoices (paid, pending, partial)`)
  console.log(`  - ${payments.length} Payment transactions`)
  console.log(`🔔 System Activity:`)
  console.log(`  - ${notifications.length} Notifications`)
  console.log(`  - ${auditLogs.length} Audit log entries`)

  console.log(`\n🔐 Test login credentials:`)
  console.log(`📧 Email/Password Authentication:`)
  console.log(`  Admin: <EMAIL> | Password: password123`)
  console.log(`  Staff: <EMAIL> | Password: password123`)
  console.log(`  Dentist: <EMAIL> | Password: password123`)
  console.log(`  Patient: <EMAIL> | Password: password123`)
  console.log(`\n🔗 Google OAuth Compatible Accounts:`)
  console.log(`  - <EMAIL> (Patient)`)
  console.log(`  - <EMAIL> (Patient)`)
  console.log(`  - <EMAIL> (Patient)`)
  console.log(`  - <EMAIL> (Patient)`)
  console.log(`  - <EMAIL> (Admin - also has OAuth)`)
  console.log(`  - <EMAIL> (Dentist - also has OAuth)`)

  console.log(`\n🎯 Ready for testing:`)
  console.log(`  ✓ Complete appointment booking workflow`)
  console.log(`  ✓ Google Sign-in integration`)
  console.log(`  ✓ Payment processing simulation`)
  console.log(`  ✓ Multi-role user management`)
  console.log(`  ✓ Dental records and treatment history`)
  console.log(`  ✓ Enhanced schedule management system:`)
  console.log(`    - ${workingHours.length} working hour configurations`)
  console.log(`    - ${timeBlocks.length} time blocks for procedures and breaks`)
  console.log(`    - ${timeOffRecords.length} time-off records (vacation, conferences, sick leave)`)
  console.log(`    - ${scheduleTemplates.length} schedule templates for recurring patterns`)
  console.log(`  ✓ Philippine timezone (Asia/Manila, UTC+8) support`)
  console.log(`  ✓ Comprehensive sample data for all features`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
